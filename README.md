# <PERSON><PERSON> thống Quản lý Khách sạn - Admin Panel

## <PERSON><PERSON> tả
Đây là một hệ thống quản lý khách sạn hoàn chỉnh được xây dựng bằng HTML, CSS và JavaScript thuần. <PERSON>ệ thống cung cấp giao diện admin để quản lý tất cả các hoạt động của khách sạn.

## Tính năng chính

### 🏨 1. <PERSON><PERSON><PERSON><PERSON> lý Đặt phòng (Booking Management)
- Đặt phòng trước (online/offline)
- Kiểm tra tình trạng phòng trống
- <PERSON><PERSON><PERSON>h<PERSON><PERSON>, hủy đặt phòng
- Gửi email/SMS xác nhận đặt phòng

### 🛏️ 2. <PERSON><PERSON><PERSON><PERSON> lý <PERSON> (Room Management)
- <PERSON><PERSON> sách phòng, lo<PERSON><PERSON> phòng (Standard, Deluxe, Suite)
- Tình trạng phòng (trống, đã đặt, đang sử dụ<PERSON>, cầ<PERSON> b<PERSON><PERSON> tr<PERSON>)
- <PERSON><PERSON><PERSON> phòng theo loại phòng
- <PERSON><PERSON><PERSON><PERSON> lý tiện nghi phòng

### 👤 3. <PERSON><PERSON><PERSON><PERSON> lý <PERSON> (Customer Management)
- Thông tin khách hàng (CMND/CCCD, hộ chiếu, quốc tịch)
- Lịch sử lưu trú
- Phân loại khách VIP (Platinum, Gold, Silver)
- Ghi chú đặc biệt

### 💵 4. Quản lý Thanh toán (Billing & Payment)
- Tính tiền theo thời gian lưu trú và dịch vụ sử dụng
- Hóa đơn tự động
- Nhiều hình thức thanh toán
- Theo dõi trạng thái thanh toán

### 🧾 5. Quản lý Dịch vụ (Service Management)
- Dịch vụ ăn uống, giặt là, spa, đưa đón sân bay
- Đặt dịch vụ, tính phí vào hóa đơn
- Quản lý tình trạng dịch vụ

### 👨‍💼 6. Quản lý Nhân sự (Staff Management)
- Thông tin nhân viên, phân quyền truy cập
- Phân ca làm việc
- Theo dõi hiệu suất

### 📊 7. Báo cáo & Thống kê (Reporting & Analytics)
- Doanh thu theo ngày/tháng/năm
- Tỷ lệ lấp đầy phòng
- Hiệu suất hoạt động
- Phân tích khách hàng

### ⚙️ 8. Hệ thống và Bảo mật (System & Security)
- Phân quyền người dùng
- Cài đặt hệ thống
- Quản lý chính sách khách sạn

## Cấu trúc dự án

```
quanlykhachsan/
├── index.html              # Trang chính
├── css/
│   └── style.css           # Stylesheet chính
├── js/
│   ├── app.js              # Logic ứng dụng
│   └── data.js             # Mock data
└── README.md               # Hướng dẫn
```

## Cách sử dụng

### 1. Mở ứng dụng
- Mở file `index.html` trong trình duyệt web
- Hoặc sử dụng Live Server trong VS Code

### 2. Điều hướng
- Sử dụng sidebar bên trái để chuyển đổi giữa các module
- Click vào menu hamburger để ẩn/hiện sidebar trên mobile

### 3. Các chức năng chính

#### Dashboard
- Xem tổng quan thống kê
- Biểu đồ tỷ lệ lấp đầy phòng
- Danh sách đặt phòng gần đây

#### Quản lý Đặt phòng
- Xem danh sách đặt phòng
- Thêm đặt phòng mới
- Chỉnh sửa/hủy đặt phòng

#### Quản lý Phòng
- Xem danh sách phòng dạng card
- Thêm phòng mới
- Thay đổi trạng thái phòng

#### Quản lý Khách hàng
- Xem danh sách khách hàng
- Thêm khách hàng mới
- Xem lịch sử lưu trú

#### Thanh toán
- Xem danh sách hóa đơn
- Tạo hóa đơn mới
- In hóa đơn

#### Dịch vụ
- Xem danh sách dịch vụ
- Thêm dịch vụ mới
- Bật/tắt dịch vụ

#### Nhân sự
- Quản lý thông tin nhân viên
- Thêm nhân viên mới
- Xem lịch làm việc

#### Báo cáo
- Xem báo cáo doanh thu
- Thống kê tỷ lệ lấp đầy
- Xuất báo cáo

#### Cài đặt
- Cài đặt thông tin khách sạn
- Quản lý chính sách
- Quản lý người dùng

## Tính năng kỹ thuật

### Responsive Design
- Tương thích với desktop, tablet và mobile
- Sidebar có thể thu gọn trên màn hình nhỏ

### Hiệu ứng UI/UX
- Smooth transitions và animations
- Hover effects
- Loading states
- Modal dialogs

### Mock Data
- Dữ liệu mẫu đầy đủ cho demo
- Các hàm utility để thao tác dữ liệu
- Format tiền tệ và ngày tháng

### Browser Support
- Chrome, Firefox, Safari, Edge
- ES6+ features
- CSS Grid và Flexbox

## Customization

### Thay đổi màu sắc
Chỉnh sửa CSS variables trong `css/style.css`:
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
}
```

### Thêm dữ liệu
Chỉnh sửa file `js/data.js` để thêm/sửa mock data.

### Thêm tính năng
Thêm functions mới trong `js/app.js` và cập nhật HTML tương ứng.

## Lưu ý cho sinh viên

### Code dễ hiểu
- Sử dụng tên biến và hàm có ý nghĩa
- Comment đầy đủ
- Cấu trúc code rõ ràng

### Hiệu ứng đơn giản
- CSS transitions cơ bản
- Hover effects
- Fade in/out animations

### Không sử dụng framework
- Vanilla JavaScript
- CSS thuần
- HTML semantic

## Demo Data

Hệ thống đã được tích hợp sẵn dữ liệu mẫu bao gồm:
- 6 phòng với các loại khác nhau
- 3 khách hàng mẫu
- 3 đặt phòng
- 4 dịch vụ
- 3 nhân viên
- 2 hóa đơn
- Báo cáo thống kê

## Hỗ trợ

Nếu có thắc mắc về code hoặc cần hỗ trợ thêm tính năng, vui lòng tạo issue hoặc liên hệ.

## License

MIT License - Tự do sử dụng cho mục đích học tập và thương mại.
