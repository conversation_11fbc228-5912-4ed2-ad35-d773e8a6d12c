<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - <PERSON><PERSON> thống <PERSON> l<PERSON> sạn</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <button class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="logo">🏨 Hotel Admin</h1>
        </div>
        <div class="header-right">
            <div class="user-info">
                <span>Xin chào, Admin</span>
                <img src="https://via.placeholder.com/40" alt="Avatar" class="avatar">
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <nav class="nav-menu">
            <ul>
                <li class="nav-item active">
                    <a href="#dashboard" class="nav-link" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#booking" class="nav-link" data-page="booking">
                        <i class="fas fa-calendar-check"></i>
                        <span>Quản lý Đặt phòng</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#rooms" class="nav-link" data-page="rooms">
                        <i class="fas fa-bed"></i>
                        <span>Quản lý Phòng</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#customers" class="nav-link" data-page="customers">
                        <i class="fas fa-users"></i>
                        <span>Quản lý Khách hàng</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#billing" class="nav-link" data-page="billing">
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span>Thanh toán</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#services" class="nav-link" data-page="services">
                        <i class="fas fa-concierge-bell"></i>
                        <span>Dịch vụ</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#staff" class="nav-link" data-page="staff">
                        <i class="fas fa-user-tie"></i>
                        <span>Nhân sự</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#reports" class="nav-link" data-page="reports">
                        <i class="fas fa-chart-bar"></i>
                        <span>Báo cáo</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#settings" class="nav-link" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>Cài đặt</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Dashboard Content -->
        <div class="page-content active" id="dashboard">
            <div class="page-header">
                <h2>Dashboard</h2>
                <p>Tổng quan hệ thống quản lý khách sạn</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-bed"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalRooms">50</h3>
                        <p>Tổng số phòng</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalBookings">25</h3>
                        <p>Đặt phòng hôm nay</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalCustomers">150</h3>
                        <p>Khách hàng</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="todayRevenue">15,500,000</h3>
                        <p>Doanh thu hôm nay (VNĐ)</p>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-section">
                <div class="chart-container">
                    <h3>Tỷ lệ lấp đầy phòng</h3>
                    <div class="occupancy-chart">
                        <div class="chart-bar">
                            <div class="bar" style="height: 80%;" data-value="80%">
                                <span class="bar-label">T2</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar" style="height: 65%;" data-value="65%">
                                <span class="bar-label">T3</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar" style="height: 90%;" data-value="90%">
                                <span class="bar-label">T4</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar" style="height: 75%;" data-value="75%">
                                <span class="bar-label">T5</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar" style="height: 95%;" data-value="95%">
                                <span class="bar-label">T6</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar" style="height: 100%;" data-value="100%">
                                <span class="bar-label">T7</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar" style="height: 85%;" data-value="85%">
                                <span class="bar-label">CN</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="recent-bookings">
                    <h3>Đặt phòng gần đây</h3>
                    <div class="booking-list" id="recentBookings">
                        <!-- Sẽ được load bằng JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Other page contents will be loaded here -->
        <div class="page-content" id="booking">
            <div class="page-header">
                <h2>Quản lý Đặt phòng</h2>
                <button class="btn btn-primary" onclick="openBookingModal()">
                    <i class="fas fa-plus"></i> Đặt phòng mới
                </button>
            </div>
            <div class="content-area">
                <div class="search-filter">
                    <input type="text" placeholder="Tìm kiếm theo tên khách hàng..." class="search-input">
                    <select class="filter-select">
                        <option value="">Tất cả trạng thái</option>
                        <option value="confirmed">Đã xác nhận</option>
                        <option value="pending">Chờ xác nhận</option>
                        <option value="cancelled">Đã hủy</option>
                    </select>
                </div>
                <div class="table-container">
                    <table class="data-table" id="bookingTable">
                        <thead>
                            <tr>
                                <th>Mã đặt phòng</th>
                                <th>Khách hàng</th>
                                <th>Phòng</th>
                                <th>Ngày nhận</th>
                                <th>Ngày trả</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="bookingTableBody">
                            <!-- Data will be loaded by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Rooms Management -->
        <div class="page-content" id="rooms">
            <div class="page-header">
                <h2>Quản lý Phòng</h2>
                <button class="btn btn-primary" onclick="openRoomModal()">
                    <i class="fas fa-plus"></i> Thêm phòng mới
                </button>
            </div>
            <div class="content-area">
                <div class="room-grid" id="roomGrid">
                    <!-- Room cards will be loaded by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Customers Management -->
        <div class="page-content" id="customers">
            <!-- Content will be loaded by JavaScript -->
        </div>

        <!-- Billing Management -->
        <div class="page-content" id="billing">
            <!-- Content will be loaded by JavaScript -->
        </div>

        <!-- Services Management -->
        <div class="page-content" id="services">
            <!-- Content will be loaded by JavaScript -->
        </div>

        <!-- Staff Management -->
        <div class="page-content" id="staff">
            <!-- Content will be loaded by JavaScript -->
        </div>

        <!-- Reports -->
        <div class="page-content" id="reports">
            <!-- Content will be loaded by JavaScript -->
        </div>

        <!-- Settings -->
        <div class="page-content" id="settings">
            <!-- Content will be loaded by JavaScript -->
        </div>
    </main>

    <!-- Modal for forms -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalBody">
                <!-- Modal content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/data.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
