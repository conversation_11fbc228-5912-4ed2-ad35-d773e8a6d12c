// Main Application JavaScript
class HotelAdmin {
    constructor() {
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadDashboard();
        this.updateStats();
    }

    setupEventListeners() {
        // Menu toggle
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        });

        // Navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.navigateToPage(page);
            });
        });

        // Responsive menu for mobile
        if (window.innerWidth <= 768) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        }

        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }
        });
    }

    navigateToPage(page) {
        // Update active nav item
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).parentElement.classList.add('active');

        // Hide all page contents
        document.querySelectorAll('.page-content').forEach(content => {
            content.classList.remove('active');
        });

        // Show selected page
        const pageElement = document.getElementById(page);
        if (pageElement) {
            pageElement.classList.add('active');
            this.currentPage = page;

            // Load page-specific content
            switch (page) {
                case 'dashboard':
                    this.loadDashboard();
                    break;
                case 'booking':
                    this.loadBookings();
                    break;
                case 'rooms':
                    this.loadRooms();
                    break;
                case 'customers':
                    this.loadCustomers();
                    break;
                case 'billing':
                    this.loadBilling();
                    break;
                case 'services':
                    this.loadServices();
                    break;
                case 'staff':
                    this.loadStaff();
                    break;
                case 'reports':
                    this.loadReports();
                    break;
                case 'settings':
                    this.loadSettings();
                    break;
            }
        }
    }

    updateStats() {
        // Update dashboard statistics
        document.getElementById('totalRooms').textContent = roomsData.length;
        document.getElementById('totalBookings').textContent = bookingsData.filter(b => 
            new Date(b.checkIn).toDateString() === new Date().toDateString()
        ).length;
        document.getElementById('totalCustomers').textContent = customersData.length;
        
        const todayRevenue = billsData
            .filter(bill => new Date(bill.issueDate).toDateString() === new Date().toDateString())
            .reduce((sum, bill) => sum + bill.totalAmount, 0);
        document.getElementById('todayRevenue').textContent = DataManager.formatCurrency(todayRevenue);
    }

    loadDashboard() {
        this.updateStats();
        this.loadRecentBookings();
    }

    loadRecentBookings() {
        const container = document.getElementById('recentBookings');
        if (!container) return;

        const recentBookings = recentBookingsData.slice(0, 5);
        
        container.innerHTML = recentBookings.map(booking => `
            <div class="booking-item" style="padding: 1rem; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <strong>${booking.customerName}</strong><br>
                    <small>Phòng ${booking.roomNumber} - ${DataManager.formatDate(booking.checkIn)}</small>
                </div>
                <div style="text-align: right;">
                    <span class="status-badge status-${booking.status}">${DataManager.getStatusText(booking.status)}</span><br>
                    <small>${DataManager.formatCurrency(booking.amount)}</small>
                </div>
            </div>
        `).join('');
    }

    loadBookings() {
        const tableBody = document.getElementById('bookingTableBody');
        if (!tableBody) return;

        tableBody.innerHTML = bookingsData.map(booking => `
            <tr>
                <td>#${booking.id.toString().padStart(4, '0')}</td>
                <td>${booking.customerName}</td>
                <td>${booking.roomNumber}</td>
                <td>${DataManager.formatDate(booking.checkIn)}</td>
                <td>${DataManager.formatDate(booking.checkOut)}</td>
                <td><span class="status-badge status-${booking.status}">${DataManager.getStatusText(booking.status)}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editBooking(${booking.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="cancelBooking(${booking.id})">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    loadRooms() {
        const roomGrid = document.getElementById('roomGrid');
        if (!roomGrid) return;

        roomGrid.innerHTML = roomsData.map(room => `
            <div class="room-card">
                <div class="room-header">
                    <h3>Phòng ${room.number}</h3>
                    <p>${room.type}</p>
                </div>
                <div class="room-body">
                    <p><strong>Giá:</strong> ${DataManager.formatCurrency(room.price)}/đêm</p>
                    <p><strong>Sức chứa:</strong> ${room.capacity} người</p>
                    <p><strong>Tiện nghi:</strong> ${room.amenities.join(', ')}</p>
                    <span class="room-status status-${room.status}">${DataManager.getStatusText(room.status)}</span>
                    <div style="margin-top: 1rem;">
                        <button class="btn btn-sm btn-primary" onclick="editRoom(${room.id})">
                            <i class="fas fa-edit"></i> Sửa
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="changeRoomStatus(${room.id})">
                            <i class="fas fa-sync"></i> Đổi trạng thái
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    loadCustomers() {
        // Tạo trang customers nếu chưa có
        this.createCustomersPage();
    }

    loadBilling() {
        // Tạo trang billing nếu chưa có
        this.createBillingPage();
    }

    loadServices() {
        // Tạo trang services nếu chưa có
        this.createServicesPage();
    }

    loadStaff() {
        // Tạo trang staff nếu chưa có
        this.createStaffPage();
    }

    loadReports() {
        // Tạo trang reports nếu chưa có
        this.createReportsPage();
    }

    loadSettings() {
        // Tạo trang settings nếu chưa có
        this.createSettingsPage();
    }

    createCustomersPage() {
        const customersPage = document.getElementById('customers');
        if (customersPage && !customersPage.querySelector('.customers-content')) {
            customersPage.innerHTML = `
                <div class="page-header">
                    <h2>Quản lý Khách hàng</h2>
                    <button class="btn btn-primary" onclick="openCustomerModal()">
                        <i class="fas fa-plus"></i> Thêm khách hàng
                    </button>
                </div>
                <div class="customers-content">
                    <div class="search-filter">
                        <input type="text" placeholder="Tìm kiếm khách hàng..." class="search-input">
                        <select class="filter-select">
                            <option value="">Tất cả cấp độ VIP</option>
                            <option value="Platinum">Platinum</option>
                            <option value="Gold">Gold</option>
                            <option value="Silver">Silver</option>
                        </select>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Tên khách hàng</th>
                                    <th>Email</th>
                                    <th>Điện thoại</th>
                                    <th>Quốc tịch</th>
                                    <th>Cấp VIP</th>
                                    <th>Tổng chi tiêu</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${customersData.map(customer => `
                                    <tr>
                                        <td>${customer.name}</td>
                                        <td>${customer.email}</td>
                                        <td>${customer.phone}</td>
                                        <td>${customer.nationality}</td>
                                        <td><span class="status-badge">${customer.vipLevel}</span></td>
                                        <td>${DataManager.formatCurrency(customer.totalSpent)}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="editCustomer(${customer.id})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="viewCustomerHistory(${customer.id})">
                                                <i class="fas fa-history"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
    }

    createBillingPage() {
        const billingPage = document.getElementById('billing');
        if (billingPage && !billingPage.querySelector('.billing-content')) {
            billingPage.innerHTML = `
                <div class="page-header">
                    <h2>Quản lý Thanh toán</h2>
                    <button class="btn btn-primary" onclick="createNewBill()">
                        <i class="fas fa-plus"></i> Tạo hóa đơn mới
                    </button>
                </div>
                <div class="billing-content">
                    <div class="search-filter">
                        <input type="text" placeholder="Tìm kiếm hóa đơn..." class="search-input">
                        <select class="filter-select">
                            <option value="">Tất cả trạng thái</option>
                            <option value="paid">Đã thanh toán</option>
                            <option value="pending">Chờ thanh toán</option>
                            <option value="overdue">Quá hạn</option>
                        </select>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Mã hóa đơn</th>
                                    <th>Khách hàng</th>
                                    <th>Tiền phòng</th>
                                    <th>Tiền dịch vụ</th>
                                    <th>Tổng tiền</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${billsData.map(bill => `
                                    <tr>
                                        <td>#${bill.id.toString().padStart(4, '0')}</td>
                                        <td>${bill.customerName}</td>
                                        <td>${DataManager.formatCurrency(bill.roomCharges)}</td>
                                        <td>${DataManager.formatCurrency(bill.serviceCharges)}</td>
                                        <td><strong>${DataManager.formatCurrency(bill.totalAmount)}</strong></td>
                                        <td><span class="status-badge status-${bill.paymentStatus}">${DataManager.getStatusText(bill.paymentStatus)}</span></td>
                                        <td>${DataManager.formatDate(bill.issueDate)}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="viewBill(${bill.id})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="printBill(${bill.id})">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
    }

    createServicesPage() {
        const servicesPage = document.getElementById('services');
        if (servicesPage && !servicesPage.querySelector('.services-content')) {
            servicesPage.innerHTML = `
                <div class="page-header">
                    <h2>Quản lý Dịch vụ</h2>
                    <button class="btn btn-primary" onclick="openServiceModal()">
                        <i class="fas fa-plus"></i> Thêm dịch vụ mới
                    </button>
                </div>
                <div class="services-content">
                    <div class="services-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 1.5rem;">
                        ${servicesData.map(service => `
                            <div class="service-card" style="background: white; border-radius: 10px; padding: 1.5rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                                <h3>${service.name}</h3>
                                <p><strong>Danh mục:</strong> ${service.category}</p>
                                <p><strong>Giá:</strong> ${DataManager.formatCurrency(service.price)}/${service.unit}</p>
                                <p>${service.description}</p>
                                <div style="margin-top: 1rem;">
                                    <span class="status-badge ${service.available ? 'status-available' : 'status-unavailable'}">
                                        ${service.available ? 'Có sẵn' : 'Không có sẵn'}
                                    </span>
                                </div>
                                <div style="margin-top: 1rem;">
                                    <button class="btn btn-sm btn-primary" onclick="editService(${service.id})">
                                        <i class="fas fa-edit"></i> Sửa
                                    </button>
                                    <button class="btn btn-sm btn-warning" onclick="toggleServiceAvailability(${service.id})">
                                        <i class="fas fa-toggle-on"></i> Bật/Tắt
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    }

    createStaffPage() {
        const staffPage = document.getElementById('staff');
        if (staffPage && !staffPage.querySelector('.staff-content')) {
            staffPage.innerHTML = `
                <div class="page-header">
                    <h2>Quản lý Nhân sự</h2>
                    <button class="btn btn-primary" onclick="openStaffModal()">
                        <i class="fas fa-plus"></i> Thêm nhân viên
                    </button>
                </div>
                <div class="staff-content">
                    <div class="search-filter">
                        <input type="text" placeholder="Tìm kiếm nhân viên..." class="search-input">
                        <select class="filter-select">
                            <option value="">Tất cả phòng ban</option>
                            <option value="Administration">Quản lý</option>
                            <option value="Front Office">Lễ tân</option>
                            <option value="Housekeeping">Dọn phòng</option>
                            <option value="F&B">Ăn uống</option>
                        </select>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Tên nhân viên</th>
                                    <th>Chức vụ</th>
                                    <th>Phòng ban</th>
                                    <th>Email</th>
                                    <th>Điện thoại</th>
                                    <th>Lương</th>
                                    <th>Ca làm</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${staffData.map(staff => `
                                    <tr>
                                        <td>${staff.name}</td>
                                        <td>${staff.position}</td>
                                        <td>${staff.department}</td>
                                        <td>${staff.email}</td>
                                        <td>${staff.phone}</td>
                                        <td>${DataManager.formatCurrency(staff.salary)}</td>
                                        <td><span class="status-badge">${staff.shift}</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="editStaff(${staff.id})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-info" onclick="viewStaffSchedule(${staff.id})">
                                                <i class="fas fa-calendar"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }
    }

    createReportsPage() {
        const reportsPage = document.getElementById('reports');
        if (reportsPage && !reportsPage.querySelector('.reports-content')) {
            reportsPage.innerHTML = `
                <div class="page-header">
                    <h2>Báo cáo & Thống kê</h2>
                    <button class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download"></i> Xuất báo cáo
                    </button>
                </div>
                <div class="reports-content">
                    <div class="report-filters" style="background: white; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3>Bộ lọc báo cáo</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                            <div class="form-group">
                                <label>Từ ngày:</label>
                                <input type="date" id="reportFromDate">
                            </div>
                            <div class="form-group">
                                <label>Đến ngày:</label>
                                <input type="date" id="reportToDate">
                            </div>
                            <div class="form-group">
                                <label>Loại báo cáo:</label>
                                <select id="reportType">
                                    <option value="revenue">Doanh thu</option>
                                    <option value="occupancy">Tỷ lệ lấp đầy</option>
                                    <option value="customer">Khách hàng</option>
                                    <option value="staff">Nhân viên</option>
                                </select>
                            </div>
                            <div class="form-group" style="display: flex; align-items: end;">
                                <button class="btn btn-primary" onclick="generateReport()">
                                    <i class="fas fa-chart-bar"></i> Tạo báo cáo
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="report-summary" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                        <div class="summary-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h4>Doanh thu tháng này</h4>
                            <h2 style="color: #28a745;">${DataManager.formatCurrency(reportsData.monthly.revenue)}</h2>
                            <p>+15% so với tháng trước</p>
                        </div>
                        <div class="summary-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h4>Tỷ lệ lấp đầy</h4>
                            <h2 style="color: #667eea;">${reportsData.monthly.occupancyRate}%</h2>
                            <p>+5% so với tháng trước</p>
                        </div>
                        <div class="summary-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h4>Số đặt phòng</h4>
                            <h2 style="color: #ffc107;">${reportsData.monthly.bookings}</h2>
                            <p>+8% so với tháng trước</p>
                        </div>
                        <div class="summary-card" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h4>Giá phòng trung bình</h4>
                            <h2 style="color: #dc3545;">${DataManager.formatCurrency(reportsData.monthly.averageRate)}</h2>
                            <p>+3% so với tháng trước</p>
                        </div>
                    </div>

                    <div class="report-charts" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3>Top loại phòng theo doanh thu</h3>
                        <div class="top-rooms" style="margin-top: 1rem;">
                            ${reportsData.monthly.topRoomTypes.map((room, index) => `
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; border-bottom: 1px solid #eee;">
                                    <div>
                                        <strong>#${index + 1} ${room.type}</strong><br>
                                        <small>${room.bookings} đặt phòng</small>
                                    </div>
                                    <div style="text-align: right;">
                                        <strong>${DataManager.formatCurrency(room.revenue)}</strong>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }
    }

    createSettingsPage() {
        const settingsPage = document.getElementById('settings');
        if (settingsPage && !settingsPage.querySelector('.settings-content')) {
            settingsPage.innerHTML = `
                <div class="page-header">
                    <h2>Cài đặt Hệ thống</h2>
                    <button class="btn btn-success" onclick="saveSettings()">
                        <i class="fas fa-save"></i> Lưu cài đặt
                    </button>
                </div>
                <div class="settings-content">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div class="settings-section" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h3>Thông tin khách sạn</h3>
                            <form id="hotelInfoForm">
                                <div class="form-group">
                                    <label>Tên khách sạn:</label>
                                    <input type="text" name="name" value="${settingsData.hotel.name}">
                                </div>
                                <div class="form-group">
                                    <label>Địa chỉ:</label>
                                    <textarea name="address" rows="3">${settingsData.hotel.address}</textarea>
                                </div>
                                <div class="form-group">
                                    <label>Điện thoại:</label>
                                    <input type="text" name="phone" value="${settingsData.hotel.phone}">
                                </div>
                                <div class="form-group">
                                    <label>Email:</label>
                                    <input type="email" name="email" value="${settingsData.hotel.email}">
                                </div>
                                <div class="form-group">
                                    <label>Website:</label>
                                    <input type="text" name="website" value="${settingsData.hotel.website}">
                                </div>
                                <div class="form-group">
                                    <label>Thuế VAT (%):</label>
                                    <input type="number" name="taxRate" value="${settingsData.hotel.taxRate}">
                                </div>
                            </form>
                        </div>

                        <div class="settings-section" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <h3>Chính sách khách sạn</h3>
                            <form id="policiesForm">
                                <div class="form-group">
                                    <label>Giờ nhận phòng:</label>
                                    <input type="time" name="checkInTime" value="${settingsData.policies.checkInTime}">
                                </div>
                                <div class="form-group">
                                    <label>Giờ trả phòng:</label>
                                    <input type="time" name="checkOutTime" value="${settingsData.policies.checkOutTime}">
                                </div>
                                <div class="form-group">
                                    <label>Chính sách hủy phòng:</label>
                                    <textarea name="cancellationPolicy" rows="3">${settingsData.policies.cancellationPolicy}</textarea>
                                </div>
                                <div class="form-group">
                                    <label>Chính sách trẻ em:</label>
                                    <textarea name="childPolicy" rows="3">${settingsData.policies.childPolicy}</textarea>
                                </div>
                                <div class="form-group">
                                    <label>Chính sách thú cưng:</label>
                                    <textarea name="petPolicy" rows="3">${settingsData.policies.petPolicy}</textarea>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="user-management" style="background: white; padding: 1.5rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-top: 2rem;">
                        <h3>Quản lý người dùng</h3>
                        <div style="margin-bottom: 1rem;">
                            <button class="btn btn-primary" onclick="openUserModal()">
                                <i class="fas fa-plus"></i> Thêm người dùng
                            </button>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Tên đăng nhập</th>
                                        <th>Họ tên</th>
                                        <th>Email</th>
                                        <th>Vai trò</th>
                                        <th>Đăng nhập cuối</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${settingsData.users.map(user => `
                                        <tr>
                                            <td>${user.username}</td>
                                            <td>${user.name}</td>
                                            <td>${user.email}</td>
                                            <td><span class="status-badge">${user.role}</span></td>
                                            <td>${user.lastLogin}</td>
                                            <td><span class="status-badge status-${user.status}">${user.status}</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="editUser(${user.id})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
        }
    }
}

// Modal functions
function openBookingModal() {
    const modal = document.getElementById('modal');
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h3>Đặt phòng mới</h3>
        <form id="bookingForm">
            <div class="form-group">
                <label>Khách hàng:</label>
                <select name="customerId" required>
                    <option value="">Chọn khách hàng</option>
                    ${customersData.map(customer => `
                        <option value="${customer.id}">${customer.name}</option>
                    `).join('')}
                </select>
            </div>
            <div class="form-group">
                <label>Phòng:</label>
                <select name="roomId" required>
                    <option value="">Chọn phòng</option>
                    ${roomsData.filter(room => room.status === 'available').map(room => `
                        <option value="${room.id}">Phòng ${room.number} - ${room.type} (${DataManager.formatCurrency(room.price)})</option>
                    `).join('')}
                </select>
            </div>
            <div class="form-group">
                <label>Ngày nhận phòng:</label>
                <input type="date" name="checkIn" required>
            </div>
            <div class="form-group">
                <label>Ngày trả phòng:</label>
                <input type="date" name="checkOut" required>
            </div>
            <div class="form-group">
                <label>Yêu cầu đặc biệt:</label>
                <textarea name="specialRequests" rows="3"></textarea>
            </div>
            <div style="text-align: right; margin-top: 1rem;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Hủy</button>
                <button type="submit" class="btn btn-primary">Đặt phòng</button>
            </div>
        </form>
    `;
    
    modal.style.display = 'block';
}

function openRoomModal() {
    const modal = document.getElementById('modal');
    const modalBody = document.getElementById('modalBody');
    
    modalBody.innerHTML = `
        <h3>Thêm phòng mới</h3>
        <form id="roomForm">
            <div class="form-group">
                <label>Số phòng:</label>
                <input type="text" name="number" required>
            </div>
            <div class="form-group">
                <label>Loại phòng:</label>
                <select name="type" required>
                    <option value="Standard">Standard</option>
                    <option value="Deluxe">Deluxe</option>
                    <option value="Suite">Suite</option>
                </select>
            </div>
            <div class="form-group">
                <label>Giá phòng (VNĐ):</label>
                <input type="number" name="price" required>
            </div>
            <div class="form-group">
                <label>Sức chứa:</label>
                <input type="number" name="capacity" required>
            </div>
            <div class="form-group">
                <label>Mô tả:</label>
                <textarea name="description" rows="3"></textarea>
            </div>
            <div style="text-align: right; margin-top: 1rem;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Hủy</button>
                <button type="submit" class="btn btn-primary">Thêm phòng</button>
            </div>
        </form>
    `;
    
    modal.style.display = 'block';
}

function openCustomerModal() {
    const modal = document.getElementById('modal');
    const modalBody = document.getElementById('modalBody');

    modalBody.innerHTML = `
        <h3>Thêm khách hàng mới</h3>
        <form id="customerForm">
            <div class="form-group">
                <label>Họ tên:</label>
                <input type="text" name="name" required>
            </div>
            <div class="form-group">
                <label>Email:</label>
                <input type="email" name="email" required>
            </div>
            <div class="form-group">
                <label>Điện thoại:</label>
                <input type="tel" name="phone" required>
            </div>
            <div class="form-group">
                <label>CMND/CCCD:</label>
                <input type="text" name="idCard" required>
            </div>
            <div class="form-group">
                <label>Quốc tịch:</label>
                <input type="text" name="nationality" value="Việt Nam">
            </div>
            <div class="form-group">
                <label>Địa chỉ:</label>
                <textarea name="address" rows="3"></textarea>
            </div>
            <div style="text-align: right; margin-top: 1rem;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Hủy</button>
                <button type="submit" class="btn btn-primary">Thêm khách hàng</button>
            </div>
        </form>
    `;

    modal.style.display = 'block';
}

function openServiceModal() {
    const modal = document.getElementById('modal');
    const modalBody = document.getElementById('modalBody');

    modalBody.innerHTML = `
        <h3>Thêm dịch vụ mới</h3>
        <form id="serviceForm">
            <div class="form-group">
                <label>Tên dịch vụ:</label>
                <input type="text" name="name" required>
            </div>
            <div class="form-group">
                <label>Danh mục:</label>
                <select name="category" required>
                    <option value="F&B">Ăn uống</option>
                    <option value="Laundry">Giặt là</option>
                    <option value="Wellness">Spa & Wellness</option>
                    <option value="Transport">Vận chuyển</option>
                    <option value="Other">Khác</option>
                </select>
            </div>
            <div class="form-group">
                <label>Giá (VNĐ):</label>
                <input type="number" name="price" required>
            </div>
            <div class="form-group">
                <label>Đơn vị:</label>
                <input type="text" name="unit" placeholder="VD: suất, kg, giờ..." required>
            </div>
            <div class="form-group">
                <label>Mô tả:</label>
                <textarea name="description" rows="3"></textarea>
            </div>
            <div style="text-align: right; margin-top: 1rem;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Hủy</button>
                <button type="submit" class="btn btn-primary">Thêm dịch vụ</button>
            </div>
        </form>
    `;

    modal.style.display = 'block';
}

function openStaffModal() {
    const modal = document.getElementById('modal');
    const modalBody = document.getElementById('modalBody');

    modalBody.innerHTML = `
        <h3>Thêm nhân viên mới</h3>
        <form id="staffForm">
            <div class="form-group">
                <label>Họ tên:</label>
                <input type="text" name="name" required>
            </div>
            <div class="form-group">
                <label>Chức vụ:</label>
                <input type="text" name="position" required>
            </div>
            <div class="form-group">
                <label>Phòng ban:</label>
                <select name="department" required>
                    <option value="Administration">Quản lý</option>
                    <option value="Front Office">Lễ tân</option>
                    <option value="Housekeeping">Dọn phòng</option>
                    <option value="F&B">Ăn uống</option>
                    <option value="Security">Bảo vệ</option>
                </select>
            </div>
            <div class="form-group">
                <label>Email:</label>
                <input type="email" name="email" required>
            </div>
            <div class="form-group">
                <label>Điện thoại:</label>
                <input type="tel" name="phone" required>
            </div>
            <div class="form-group">
                <label>Lương (VNĐ):</label>
                <input type="number" name="salary" required>
            </div>
            <div class="form-group">
                <label>Ca làm việc:</label>
                <select name="shift" required>
                    <option value="morning">Ca sáng</option>
                    <option value="afternoon">Ca chiều</option>
                    <option value="night">Ca đêm</option>
                </select>
            </div>
            <div style="text-align: right; margin-top: 1rem;">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">Hủy</button>
                <button type="submit" class="btn btn-primary">Thêm nhân viên</button>
            </div>
        </form>
    `;

    modal.style.display = 'block';
}

function closeModal() {
    document.getElementById('modal').style.display = 'none';
}

// Utility functions
function editBooking(id) {
    alert(`Chỉnh sửa đặt phòng #${id}`);
}

function cancelBooking(id) {
    if (confirm('Bạn có chắc muốn hủy đặt phòng này?')) {
        alert(`Đã hủy đặt phòng #${id}`);
    }
}

function editRoom(id) {
    alert(`Chỉnh sửa phòng #${id}`);
}

function changeRoomStatus(id) {
    const room = DataManager.getRoomById(id);
    if (room) {
        const newStatus = prompt('Nhập trạng thái mới (available/occupied/maintenance):', room.status);
        if (newStatus && ['available', 'occupied', 'maintenance'].includes(newStatus)) {
            DataManager.updateRoom(id, { status: newStatus });
            app.loadRooms();
        }
    }
}

function editCustomer(id) {
    alert(`Chỉnh sửa khách hàng #${id}`);
}

function viewCustomerHistory(id) {
    alert(`Xem lịch sử khách hàng #${id}`);
}

function createNewBill() {
    alert('Tạo hóa đơn mới');
}

function viewBill(id) {
    alert(`Xem hóa đơn #${id}`);
}

function printBill(id) {
    alert(`In hóa đơn #${id}`);
}

function editService(id) {
    alert(`Chỉnh sửa dịch vụ #${id}`);
}

function toggleServiceAvailability(id) {
    const service = DataManager.getServiceById(id);
    if (service) {
        service.available = !service.available;
        app.loadServices();
        alert(`Đã ${service.available ? 'bật' : 'tắt'} dịch vụ ${service.name}`);
    }
}

function editStaff(id) {
    alert(`Chỉnh sửa nhân viên #${id}`);
}

function viewStaffSchedule(id) {
    alert(`Xem lịch làm việc nhân viên #${id}`);
}

function exportReport() {
    alert('Xuất báo cáo');
}

function generateReport() {
    alert('Tạo báo cáo');
}

function saveSettings() {
    alert('Lưu cài đặt thành công!');
}

function openUserModal() {
    alert('Thêm người dùng mới');
}

function editUser(id) {
    alert(`Chỉnh sửa người dùng #${id}`);
}

function deleteUser(id) {
    if (confirm('Bạn có chắc muốn xóa người dùng này?')) {
        alert(`Đã xóa người dùng #${id}`);
    }
}

// Initialize app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new HotelAdmin();
});

// Close modal when clicking outside
window.addEventListener('click', (e) => {
    const modal = document.getElementById('modal');
    if (e.target === modal) {
        closeModal();
    }
});
