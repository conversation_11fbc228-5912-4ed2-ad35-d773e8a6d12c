// Mock Data cho hệ thống quản lý khách sạn

// <PERSON><PERSON> liệu phòng
const roomsData = [
    {
        id: 1,
        number: "101",
        type: "Standard",
        price: 500000,
        status: "available",
        capacity: 2,
        amenities: ["WiFi", "TV", "Điều hòa", "Tủ lạnh"],
        description: "Phòng tiêu chuẩn với đầy đủ tiện nghi"
    },
    {
        id: 2,
        number: "102",
        type: "Standard",
        price: 500000,
        status: "occupied",
        capacity: 2,
        amenities: ["WiFi", "TV", "Điều hòa", "Tủ lạnh"],
        description: "Phòng tiêu chuẩn với đầy đủ tiện nghi"
    },
    {
        id: 3,
        number: "201",
        type: "Deluxe",
        price: 800000,
        status: "available",
        capacity: 3,
        amenities: ["WiFi", "TV", "Điều hòa", "T<PERSON> lạnh", "Ban công", "Minibar"],
        description: "<PERSON>òng cao cấp với view đẹp"
    },
    {
        id: 4,
        number: "202",
        type: "Deluxe",
        price: 800000,
        status: "maintenance",
        capacity: 3,
        amenities: ["WiFi", "TV", "Điều hòa", "Tủ lạnh", "Ban công", "Minibar"],
        description: "Phòng cao cấp với view đẹp"
    },
    {
        id: 5,
        number: "301",
        type: "Suite",
        price: 1500000,
        status: "available",
        capacity: 4,
        amenities: ["WiFi", "TV", "Điều hòa", "Tủ lạnh", "Ban công", "Minibar", "Jacuzzi", "Phòng khách riêng"],
        description: "Phòng suite sang trọng"
    },
    {
        id: 6,
        number: "302",
        type: "Suite",
        price: 1500000,
        status: "occupied",
        capacity: 4,
        amenities: ["WiFi", "TV", "Điều hòa", "Tủ lạnh", "Ban công", "Minibar", "Jacuzzi", "Phòng khách riêng"],
        description: "Phòng suite sang trọng"
    }
];

// Dữ liệu khách hàng
const customersData = [
    {
        id: 1,
        name: "Nguyễn Văn An",
        email: "<EMAIL>",
        phone: "0901234567",
        idCard: "123456789",
        nationality: "Việt Nam",
        address: "123 Đường ABC, Quận 1, TP.HCM",
        dateOfBirth: "1985-05-15",
        gender: "Nam",
        vipLevel: "Gold",
        totalStays: 15,
        totalSpent: 25000000,
        notes: "Khách hàng thân thiết, thích phòng tầng cao"
    },
    {
        id: 2,
        name: "Trần Thị Bình",
        email: "<EMAIL>",
        phone: "0912345678",
        idCard: "987654321",
        nationality: "Việt Nam",
        address: "456 Đường XYZ, Quận 3, TP.HCM",
        dateOfBirth: "1990-08-22",
        gender: "Nữ",
        vipLevel: "Silver",
        totalStays: 8,
        totalSpent: 12000000,
        notes: "Không hút thuốc, ăn chay"
    },
    {
        id: 3,
        name: "John Smith",
        email: "<EMAIL>",
        phone: "+1234567890",
        idCard: "P123456789",
        nationality: "Mỹ",
        address: "New York, USA",
        dateOfBirth: "1982-12-10",
        gender: "Nam",
        vipLevel: "Platinum",
        totalStays: 25,
        totalSpent: 45000000,
        notes: "Khách nước ngoài, cần phiên dịch"
    }
];

// Dữ liệu đặt phòng
const bookingsData = [
    {
        id: 1,
        customerId: 1,
        customerName: "Nguyễn Văn An",
        roomId: 1,
        roomNumber: "101",
        checkIn: "2024-01-15",
        checkOut: "2024-01-18",
        nights: 3,
        totalAmount: 1500000,
        status: "confirmed",
        bookingDate: "2024-01-10",
        specialRequests: "Phòng không hút thuốc",
        paymentStatus: "paid"
    },
    {
        id: 2,
        customerId: 2,
        customerName: "Trần Thị Bình",
        roomId: 3,
        roomNumber: "201",
        checkIn: "2024-01-20",
        checkOut: "2024-01-22",
        nights: 2,
        totalAmount: 1600000,
        status: "pending",
        bookingDate: "2024-01-18",
        specialRequests: "Giường đôi",
        paymentStatus: "pending"
    },
    {
        id: 3,
        customerId: 3,
        customerName: "John Smith",
        roomId: 5,
        roomNumber: "301",
        checkIn: "2024-01-25",
        checkOut: "2024-01-30",
        nights: 5,
        totalAmount: 7500000,
        status: "confirmed",
        bookingDate: "2024-01-15",
        specialRequests: "Late check-out",
        paymentStatus: "paid"
    }
];

// Dữ liệu dịch vụ
const servicesData = [
    {
        id: 1,
        name: "Dịch vụ ăn uống",
        category: "F&B",
        price: 200000,
        unit: "suất",
        description: "Dịch vụ ăn uống tại phòng",
        available: true
    },
    {
        id: 2,
        name: "Giặt là",
        category: "Laundry",
        price: 50000,
        unit: "kg",
        description: "Dịch vụ giặt là cao cấp",
        available: true
    },
    {
        id: 3,
        name: "Spa & Massage",
        category: "Wellness",
        price: 800000,
        unit: "giờ",
        description: "Dịch vụ spa và massage thư giãn",
        available: true
    },
    {
        id: 4,
        name: "Đưa đón sân bay",
        category: "Transport",
        price: 300000,
        unit: "chuyến",
        description: "Dịch vụ đưa đón sân bay",
        available: true
    }
];

// Dữ liệu nhân viên
const staffData = [
    {
        id: 1,
        name: "Lê Văn Cường",
        position: "Quản lý",
        department: "Administration",
        email: "<EMAIL>",
        phone: "0901111111",
        salary: 15000000,
        startDate: "2020-01-15",
        status: "active",
        shift: "morning",
        permissions: ["all"]
    },
    {
        id: 2,
        name: "Phạm Thị Dung",
        position: "Lễ tân",
        department: "Front Office",
        email: "<EMAIL>",
        phone: "0902222222",
        salary: 8000000,
        startDate: "2021-03-20",
        status: "active",
        shift: "morning",
        permissions: ["booking", "customers"]
    },
    {
        id: 3,
        name: "Hoàng Văn Em",
        position: "Housekeeping",
        department: "Housekeeping",
        email: "<EMAIL>",
        phone: "0903333333",
        salary: 6000000,
        startDate: "2022-06-10",
        status: "active",
        shift: "afternoon",
        permissions: ["rooms"]
    }
];

// Dữ liệu hóa đơn
const billsData = [
    {
        id: 1,
        bookingId: 1,
        customerId: 1,
        customerName: "Nguyễn Văn An",
        roomCharges: 1500000,
        serviceCharges: 250000,
        tax: 175000,
        discount: 0,
        totalAmount: 1925000,
        paymentMethod: "credit_card",
        paymentStatus: "paid",
        paymentDate: "2024-01-18",
        issueDate: "2024-01-18",
        services: [
            { serviceId: 1, serviceName: "Dịch vụ ăn uống", quantity: 1, amount: 200000 },
            { serviceId: 2, serviceName: "Giặt là", quantity: 1, amount: 50000 }
        ]
    },
    {
        id: 2,
        bookingId: 2,
        customerId: 2,
        customerName: "Trần Thị Bình",
        roomCharges: 1600000,
        serviceCharges: 0,
        tax: 160000,
        discount: 80000,
        totalAmount: 1680000,
        paymentMethod: "cash",
        paymentStatus: "pending",
        paymentDate: null,
        issueDate: "2024-01-22",
        services: []
    }
];

// Dữ liệu báo cáo
const reportsData = {
    daily: {
        date: "2024-01-20",
        revenue: 15500000,
        bookings: 25,
        checkIns: 12,
        checkOuts: 8,
        occupancyRate: 85,
        averageRate: 750000
    },
    monthly: {
        month: "2024-01",
        revenue: *********,
        bookings: 680,
        occupancyRate: 78,
        averageRate: 720000,
        topRoomTypes: [
            { type: "Deluxe", bookings: 280, revenue: ********* },
            { type: "Standard", bookings: 250, revenue: ********* },
            { type: "Suite", bookings: 150, revenue: ********* }
        ]
    },
    yearly: {
        year: "2024",
        revenue: 5400000000,
        bookings: 8200,
        occupancyRate: 82,
        averageRate: 750000
    }
};

// Dữ liệu cài đặt hệ thống
const settingsData = {
    hotel: {
        name: "Grand Hotel",
        address: "123 Đường Nguyễn Huệ, Quận 1, TP.HCM",
        phone: "028-1234-5678",
        email: "<EMAIL>",
        website: "www.grandhotel.com",
        taxRate: 10,
        currency: "VND"
    },
    policies: {
        checkInTime: "14:00",
        checkOutTime: "12:00",
        cancellationPolicy: "Miễn phí hủy trước 24h",
        childPolicy: "Trẻ em dưới 6 tuổi miễn phí",
        petPolicy: "Không cho phép thú cưng"
    },
    users: [
        {
            id: 1,
            username: "admin",
            name: "Administrator",
            email: "<EMAIL>",
            role: "admin",
            permissions: ["all"],
            lastLogin: "2024-01-20 09:30:00",
            status: "active"
        },
        {
            id: 2,
            username: "manager",
            name: "Hotel Manager",
            email: "<EMAIL>",
            role: "manager",
            permissions: ["booking", "rooms", "customers", "billing", "reports"],
            lastLogin: "2024-01-20 08:15:00",
            status: "active"
        },
        {
            id: 3,
            username: "receptionist",
            name: "Front Desk",
            email: "<EMAIL>",
            role: "staff",
            permissions: ["booking", "customers"],
            lastLogin: "2024-01-20 07:00:00",
            status: "active"
        }
    ]
};

// Utility functions để làm việc với data
const DataManager = {
    // Rooms
    getRooms: () => roomsData,
    getRoomById: (id) => roomsData.find(room => room.id === id),
    addRoom: (room) => {
        room.id = Math.max(...roomsData.map(r => r.id)) + 1;
        roomsData.push(room);
        return room;
    },
    updateRoom: (id, updates) => {
        const index = roomsData.findIndex(room => room.id === id);
        if (index !== -1) {
            roomsData[index] = { ...roomsData[index], ...updates };
            return roomsData[index];
        }
        return null;
    },
    deleteRoom: (id) => {
        const index = roomsData.findIndex(room => room.id === id);
        if (index !== -1) {
            return roomsData.splice(index, 1)[0];
        }
        return null;
    },

    // Customers
    getCustomers: () => customersData,
    getCustomerById: (id) => customersData.find(customer => customer.id === id),
    addCustomer: (customer) => {
        customer.id = Math.max(...customersData.map(c => c.id)) + 1;
        customersData.push(customer);
        return customer;
    },
    updateCustomer: (id, updates) => {
        const index = customersData.findIndex(customer => customer.id === id);
        if (index !== -1) {
            customersData[index] = { ...customersData[index], ...updates };
            return customersData[index];
        }
        return null;
    },

    // Bookings
    getBookings: () => bookingsData,
    getBookingById: (id) => bookingsData.find(booking => booking.id === id),
    addBooking: (booking) => {
        booking.id = Math.max(...bookingsData.map(b => b.id)) + 1;
        bookingsData.push(booking);
        return booking;
    },
    updateBooking: (id, updates) => {
        const index = bookingsData.findIndex(booking => booking.id === id);
        if (index !== -1) {
            bookingsData[index] = { ...bookingsData[index], ...updates };
            return bookingsData[index];
        }
        return null;
    },

    // Services
    getServices: () => servicesData,
    getServiceById: (id) => servicesData.find(service => service.id === id),

    // Staff
    getStaff: () => staffData,
    getStaffById: (id) => staffData.find(staff => staff.id === id),

    // Bills
    getBills: () => billsData,
    getBillById: (id) => billsData.find(bill => bill.id === id),

    // Reports
    getReports: () => reportsData,

    // Settings
    getSettings: () => settingsData,

    // Utility functions
    formatCurrency: (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    },

    formatDate: (dateString) => {
        return new Date(dateString).toLocaleDateString('vi-VN');
    },

    getStatusText: (status) => {
        const statusMap = {
            'available': 'Trống',
            'occupied': 'Đang sử dụng',
            'maintenance': 'Bảo trì',
            'confirmed': 'Đã xác nhận',
            'pending': 'Chờ xác nhận',
            'cancelled': 'Đã hủy',
            'paid': 'Đã thanh toán',
            'unpaid': 'Chưa thanh toán'
        };
        return statusMap[status] || status;
    }
};

// Thêm dữ liệu đặt phòng gần đây cho dashboard
const recentBookingsData = [
    {
        id: 1,
        customerName: "Nguyễn Văn An",
        roomNumber: "101",
        checkIn: "2024-01-20",
        status: "confirmed",
        amount: 1500000
    },
    {
        id: 2,
        customerName: "Trần Thị Bình",
        roomNumber: "201",
        checkIn: "2024-01-21",
        status: "pending",
        amount: 1600000
    },
    {
        id: 3,
        customerName: "John Smith",
        roomNumber: "301",
        checkIn: "2024-01-22",
        status: "confirmed",
        amount: 7500000
    },
    {
        id: 4,
        customerName: "Lê Thị Cẩm",
        roomNumber: "102",
        checkIn: "2024-01-23",
        status: "pending",
        amount: 500000
    }
];

// Export data for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        roomsData,
        customersData,
        bookingsData,
        servicesData,
        staffData,
        billsData,
        reportsData,
        settingsData,
        recentBookingsData,
        DataManager
    };
}
